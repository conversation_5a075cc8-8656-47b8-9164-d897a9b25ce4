#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证日志配置是否正确过滤了 GET/API 等 info 级别的日志
"""

import os
import sys
import logging
import time
import requests
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_logging_levels():
    """测试不同日志级别的输出"""
    print("=== 测试日志级别配置 ===")
    
    # 创建不同的日志记录器
    loggers = {
        'werkzeug': logging.getLogger('werkzeug'),
        'urllib3': logging.getLogger('urllib3'),
        'requests': logging.getLogger('requests'),
        'paho.mqtt': logging.getLogger('paho.mqtt'),
        'root': logging.getLogger(),
        'app': logging.getLogger('app')
    }
    
    print("\n当前日志级别配置：")
    for name, logger in loggers.items():
        level_name = logging.getLevelName(logger.level)
        effective_level = logging.getLevelName(logger.getEffectiveLevel())
        print(f"  {name:12}: 设置级别={level_name:8} 有效级别={effective_level}")
    
    print("\n测试各级别日志输出：")
    
    # 测试 werkzeug 日志（应该被过滤）
    print("\n--- 测试 werkzeug 日志 (应该只显示 WARNING 及以上) ---")
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.debug("这是 werkzeug DEBUG 日志 (应该被过滤)")
    werkzeug_logger.info("这是 werkzeug INFO 日志 (应该被过滤)")
    werkzeug_logger.warning("这是 werkzeug WARNING 日志 (应该显示)")
    werkzeug_logger.error("这是 werkzeug ERROR 日志 (应该显示)")
    
    # 测试应用日志（应该正常显示）
    print("\n--- 测试应用日志 (应该正常显示 INFO 及以上) ---")
    app_logger = logging.getLogger('app')
    app_logger.debug("这是应用 DEBUG 日志 (可能被过滤)")
    app_logger.info("这是应用 INFO 日志 (应该显示)")
    app_logger.warning("这是应用 WARNING 日志 (应该显示)")
    app_logger.error("这是应用 ERROR 日志 (应该显示)")


def test_flask_app_logging():
    """测试 Flask 应用的日志配置"""
    print("\n=== 测试 Flask 应用日志配置 ===")
    
    try:
        # 设置环境变量
        os.environ['FLASK_CONFIG'] = 'development'
        
        # 导入并创建 Flask 应用
        from app import create_app
        app = create_app('development')
        
        print("✅ Flask 应用创建成功")
        
        # 测试应用上下文中的日志
        with app.app_context():
            print("\n--- 测试 Flask 应用日志 ---")
            app.logger.debug("这是 Flask DEBUG 日志")
            app.logger.info("这是 Flask INFO 日志")
            app.logger.warning("这是 Flask WARNING 日志")
            app.logger.error("这是 Flask ERROR 日志")
            
            # 检查日志级别
            print(f"\nFlask 应用日志级别: {logging.getLevelName(app.logger.level)}")
            print(f"Flask 应用有效日志级别: {logging.getLevelName(app.logger.getEffectiveLevel())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flask 应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def simulate_http_requests():
    """模拟 HTTP 请求来测试请求日志是否被过滤"""
    print("\n=== 模拟 HTTP 请求测试 ===")
    
    try:
        # 启动一个简单的测试服务器（在后台）
        import threading
        import http.server
        import socketserver
        from urllib.parse import urlparse
        
        # 创建一个简单的处理器
        class QuietHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
            def log_message(self, format, *args):
                # 重写日志方法，测试是否被过滤
                print(f"[HTTP服务器] {format % args}")
        
        # 启动测试服务器
        PORT = 8888
        Handler = QuietHTTPRequestHandler
        
        def start_server():
            with socketserver.TCPServer(("", PORT), Handler) as httpd:
                print(f"测试服务器启动在端口 {PORT}")
                httpd.timeout = 5  # 5秒超时
                httpd.handle_request()  # 只处理一个请求
        
        # 在后台启动服务器
        server_thread = threading.Thread(target=start_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(1)
        
        # 发送测试请求
        print("\n发送测试 HTTP 请求...")
        try:
            response = requests.get(f"http://localhost:{PORT}/", timeout=3)
            print(f"请求状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"请求异常（预期）: {e}")
        
        # 等待服务器线程结束
        server_thread.join(timeout=2)
        
        return True
        
    except Exception as e:
        print(f"❌ HTTP 请求测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔍 测试日志配置...")
    
    # 首先应用我们的日志配置
    print("\n应用日志配置...")
    
    # 设置 Werkzeug (Flask 开发服务器) 日志级别为 WARNING
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    
    # 设置 urllib3 日志级别为 WARNING
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # 设置 requests 日志级别为 WARNING
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    # 设置 paho.mqtt 日志级别为 WARNING
    logging.getLogger('paho.mqtt').setLevel(logging.WARNING)
    
    # 设置根日志级别为 INFO
    logging.getLogger().setLevel(logging.INFO)
    
    print("✅ 日志配置应用完成")
    
    success = True
    
    # 测试1: 日志级别
    try:
        test_logging_levels()
    except Exception as e:
        print(f"❌ 日志级别测试失败: {e}")
        success = False
    
    # 测试2: Flask 应用日志
    try:
        if not test_flask_app_logging():
            success = False
    except Exception as e:
        print(f"❌ Flask 应用日志测试失败: {e}")
        success = False
    
    # 测试3: HTTP 请求日志
    try:
        if not simulate_http_requests():
            success = False
    except Exception as e:
        print(f"❌ HTTP 请求日志测试失败: {e}")
        success = False
    
    if success:
        print("\n🎉 所有日志配置测试通过！")
        print("\n📝 配置效果：")
        print("  ✅ werkzeug 日志 (GET/POST 请求) 已过滤到 WARNING 级别")
        print("  ✅ urllib3 日志 (HTTP 连接) 已过滤到 WARNING 级别")
        print("  ✅ requests 日志已过滤到 WARNING 级别")
        print("  ✅ paho.mqtt 日志已过滤到 WARNING 级别")
        print("  ✅ 应用日志仍然正常显示 INFO 级别")
    else:
        print("\n❌ 部分日志配置测试失败")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
