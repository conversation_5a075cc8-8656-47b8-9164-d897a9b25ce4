#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：检查工作流中的工作区准备节点是否被正确调用
"""

import os
import sys
import tempfile
from pathlib import Path

# 设置环境变量以避免API密钥错误
os.environ['LANGCHAIN_API_KEY'] = 'dummy_key_for_testing'
os.environ['LANGCHAIN_BASE_URL'] = 'https://dummy.url'

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from app.langgraph_def.graph_builder import build_graph, prepare_workspace_node
    from app.langgraph_def.agent_state import AgentState
    print("✅ 成功导入模块")
except Exception as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)


def test_workflow_structure():
    """测试工作流结构是否正确"""
    print("\n=== 测试工作流结构 ===")
    
    try:
        # 构建工作流图
        graph = build_graph()
        print("✅ 成功构建工作流图")
        
        # 检查节点是否存在
        nodes = graph.nodes
        print(f"工作流包含的节点: {list(nodes.keys())}")
        
        if "prepare_workspace" in nodes:
            print("✅ prepare_workspace 节点已注册")
        else:
            print("❌ prepare_workspace 节点未找到")
            return False
            
        # 检查边连接
        edges = graph.edges
        print(f"工作流边连接数量: {len(edges)}")
        
        # 查找与 prepare_workspace 相关的边
        prepare_workspace_edges = []
        for edge in edges:
            if hasattr(edge, 'source') and hasattr(edge, 'target'):
                if edge.source == "prepare_workspace" or edge.target == "prepare_workspace":
                    prepare_workspace_edges.append(f"{edge.source} -> {edge.target}")
        
        if prepare_workspace_edges:
            print("✅ prepare_workspace 节点的连接:")
            for edge in prepare_workspace_edges:
                print(f"  - {edge}")
        else:
            print("❌ 未找到 prepare_workspace 节点的连接")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试工作流结构失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_prepare_workspace_function():
    """直接测试 prepare_workspace_node 函数"""
    print("\n=== 测试 prepare_workspace_node 函数 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_workspace = Path(temp_dir)
        print(f"使用临时工作目录: {temp_workspace}")
        
        # 创建测试状态
        test_state = {
            'current_device_task': {
                'internal_device_id': 'test_device_001',
                'device_role': 'Test Light Sensor'
            },
            'project_files': {
                'test_device_001': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/test_sensor.h': '#ifndef TEST_SENSOR_H\n#define TEST_SENSOR_H\n#endif'
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        try:
            # 调用函数
            result = prepare_workspace_node(test_state)
            print(f"✅ 函数调用成功，返回结果: {result}")
            
            # 验证结果
            if 'build_dir' in result:
                build_dir = Path(result['build_dir'])
                print(f"✅ 返回了 build_dir: {build_dir}")
                
                if build_dir.exists():
                    print("✅ build_dir 目录存在")
                    
                    # 检查文件是否正确创建
                    expected_files = [
                        'src/main.cpp',
                        'platformio.ini', 
                        'lib/test_sensor.h'
                    ]
                    
                    all_files_exist = True
                    for file_path in expected_files:
                        full_path = build_dir / file_path
                        if full_path.exists():
                            print(f"✅ 文件存在: {file_path}")
                        else:
                            print(f"❌ 文件缺失: {file_path}")
                            all_files_exist = False
                    
                    if all_files_exist:
                        print("✅ 所有预期文件都已正确创建")
                        
                        # 显示目录结构
                        print("\n--- 创建的目录结构 ---")
                        for root, dirs, files in os.walk(build_dir):
                            level = root.replace(str(build_dir), '').count(os.sep)
                            indent = ' ' * 2 * level
                            print(f"{indent}{os.path.basename(root)}/")
                            subindent = ' ' * 2 * (level + 1)
                            for file in files:
                                print(f"{subindent}{file}")
                        
                        return True
                    else:
                        print("❌ 部分文件创建失败")
                        return False
                else:
                    print(f"❌ build_dir 目录不存在: {build_dir}")
                    return False
            else:
                print("❌ 函数未返回 build_dir")
                return False
                
        except Exception as e:
            print(f"❌ 函数调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主测试函数"""
    print("🔍 开始调试工作区隔离功能...")
    
    success = True
    
    # 测试1: 工作流结构
    if not test_workflow_structure():
        success = False
    
    # 测试2: 函数功能
    if not test_prepare_workspace_function():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！工作区隔离功能应该正常工作。")
        print("\n💡 如果在实际运行中仍然出现问题，可能的原因：")
        print("   1. 工作流执行顺序问题")
        print("   2. 状态传递问题")
        print("   3. 其他节点覆盖了 build_dir")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 调试脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
