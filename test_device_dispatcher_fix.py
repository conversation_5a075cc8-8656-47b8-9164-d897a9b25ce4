#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证 device_dispatcher_node 修复后不再重置 build_dir
"""

import os
import sys
import tempfile
import re
from pathlib import Path

# 设置环境变量以避免API密钥错误
os.environ['LANGCHAIN_API_KEY'] = 'dummy_key_for_testing'
os.environ['LANGCHAIN_BASE_URL'] = 'https://dummy.url'

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def robust_rmtree(path: Path, retries: int = 5, delay: int = 1):
    """
    一个更健壮的删除函数，可以处理文件和目录，并在遇到权限错误时重试。
    """
    import time
    import shutil
    for i in range(retries):
        if not path.exists():
            return

        try:
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()

            if not path.exists():
                return

        except (PermissionError, OSError) as e:
            print(f"  [robust_rmtree] Warn: Attempt {i + 1}/{retries} to delete '{path}' failed: {e}. Retrying in {delay}s...")
            time.sleep(delay)

    if path.exists():
        raise OSError(f"Failed to delete path '{path}' after {retries} retries. It might be locked by another process.")


def prepare_workspace_node(state: dict) -> dict:
    """
    为当前设备创建一个隔离的子目录，并将所有项目文件写入其中。
    """
    device_task = state['current_device_task']
    device_id = device_task['internal_device_id']
    device_role = device_task.get('device_role', device_id)
    safe_device_role = re.sub(r'[^a-zA-Z0-9_-]', '', device_role.lower().replace(' ', '_'))
    
    print(f"\n--- PREPARING WORKSPACE: Creating isolated directory for device '{device_role}' ---")

    project_files = state['project_files'][device_id]
    base_workspace_path = Path(state['workspace_path'])
    device_project_path = base_workspace_path / safe_device_role
    
    # 清理旧目录（如果存在），确保每次都是全新的开始
    if device_project_path.exists():
        robust_rmtree(device_project_path)
    device_project_path.mkdir(parents=True, exist_ok=True)
    
    print(f"  -> Writing project files to: '{device_project_path}'")

    for filename, content in project_files.items():
        dest_path = device_project_path / filename
        if filename.endswith('/'):
            dest_path.mkdir(parents=True, exist_ok=True)
            continue
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        dest_path.write_text(content, encoding="utf-8")
    
    # 将准备好的、设备专用的路径作为 build_dir 返回，供后续节点使用
    return {"build_dir": str(device_project_path)}


def mock_device_dispatcher_node(state: dict) -> dict:
    """
    模拟修复后的 device_dispatcher_node（简化版）
    """
    queue = state.get('device_tasks_queue', [])
    if not queue:
        return {"current_device_task": None}

    next_device_task = queue[0]
    remaining_queue = queue[1:]

    # 保留需要在设备间传递的持久化状态
    persistent_state = {
        "workspace_path": state.get('workspace_path'),
        "project_files": state.get('project_files', {})
    }

    # 构建最终的返回字典 - 注意：不再重置 build_dir
    update_dict = {
        **persistent_state,
        "device_tasks_queue": remaining_queue,
        "current_device_task": next_device_task,
        # 显式重置所有临时开发状态
        "module_tasks": [],
        "current_module_task": None,
        "completed_modules": {},
        "feedback": "",
        "original_module_plan": None,
        "current_api_spec": None,
        "test_plan": None,
        # 注意：不再重置 build_dir，让 prepare_workspace_node 来设置它
        # "build_dir": "",  # ❌ 移除这行，避免覆盖 prepare_workspace_node 的设置
        "firmware_path": None,
    }

    return update_dict


def test_workflow_sequence():
    """测试工作流序列：device_dispatcher -> prepare_workspace"""
    print("=== 测试工作流序列：device_dispatcher -> prepare_workspace ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_workspace = Path(temp_dir)
        print(f"使用临时工作目录: {temp_workspace}")
        
        # 创建初始状态，模拟两个设备
        initial_state = {
            'device_tasks_queue': [
                {
                    'internal_device_id': 'device_001',
                    'device_role': 'Light Sensor Node'
                },
                {
                    'internal_device_id': 'device_002', 
                    'device_role': 'Display Controller'
                }
            ],
            'project_files': {
                'device_001': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/sensor.h': '#ifndef SENSOR_H\n#define SENSOR_H\n#endif'
                },
                'device_002': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/display.h': '#ifndef DISPLAY_H\n#define DISPLAY_H\n#endif'
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        # 模拟第一个设备的处理流程
        print("\n--- 处理设备1 ---")
        
        # 1. device_dispatcher_node 选择第一个设备
        state_after_dispatcher = mock_device_dispatcher_node(initial_state)
        print(f"device_dispatcher 返回的状态中 build_dir: '{state_after_dispatcher.get('build_dir', 'NOT_SET')}'")
        
        # 2. prepare_workspace_node 为设备创建工作区
        state_after_workspace = prepare_workspace_node(state_after_dispatcher)
        print(f"prepare_workspace 返回的状态中 build_dir: '{state_after_workspace.get('build_dir', 'NOT_SET')}'")
        
        # 合并状态（模拟 LangGraph 的状态合并）
        merged_state = {**state_after_dispatcher, **state_after_workspace}
        
        # 验证第一个设备的 build_dir
        device1_build_dir = Path(merged_state['build_dir'])
        assert device1_build_dir.exists(), "设备1的 build_dir 应该存在"
        assert device1_build_dir.name == 'light_sensor_node', f"设备1目录名应为 'light_sensor_node'，实际为 '{device1_build_dir.name}'"
        assert (device1_build_dir / 'lib/sensor.h').exists(), "设备1应该有 sensor.h 文件"
        print(f"✅ 设备1工作区正确创建: {device1_build_dir}")
        
        # 模拟第二个设备的处理流程
        print("\n--- 处理设备2 ---")
        
        # 1. device_dispatcher_node 选择第二个设备
        state_after_dispatcher2 = mock_device_dispatcher_node(merged_state)
        print(f"device_dispatcher 返回的状态中 build_dir: '{state_after_dispatcher2.get('build_dir', 'NOT_SET')}'")
        
        # 2. prepare_workspace_node 为设备创建工作区
        state_after_workspace2 = prepare_workspace_node(state_after_dispatcher2)
        print(f"prepare_workspace 返回的状态中 build_dir: '{state_after_workspace2.get('build_dir', 'NOT_SET')}'")
        
        # 合并状态
        merged_state2 = {**state_after_dispatcher2, **state_after_workspace2}
        
        # 验证第二个设备的 build_dir
        device2_build_dir = Path(merged_state2['build_dir'])
        assert device2_build_dir.exists(), "设备2的 build_dir 应该存在"
        assert device2_build_dir.name == 'display_controller', f"设备2目录名应为 'display_controller'，实际为 '{device2_build_dir.name}'"
        assert (device2_build_dir / 'lib/display.h').exists(), "设备2应该有 display.h 文件"
        print(f"✅ 设备2工作区正确创建: {device2_build_dir}")
        
        # 验证隔离性：两个设备的目录应该不同且独立
        assert device1_build_dir != device2_build_dir, "两个设备应该有不同的工作目录"
        assert not (device1_build_dir / 'lib/display.h').exists(), "设备1不应该包含设备2的文件"
        assert not (device2_build_dir / 'lib/sensor.h').exists(), "设备2不应该包含设备1的文件"
        
        print("\n✅ 所有测试通过！设备工作区隔离功能正常工作。")
        print(f"设备1目录: {device1_build_dir}")
        print(f"设备2目录: {device2_build_dir}")
        
        return True


def main():
    """主测试函数"""
    print("🔍 测试 device_dispatcher_node 修复后的工作区隔离功能...")
    
    try:
        success = test_workflow_sequence()
        if success:
            print("\n🎉 修复成功！现在每个设备都会有独立的工作目录。")
        return success
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
