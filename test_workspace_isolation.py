#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证设备工作区隔离功能是否正常工作
"""

import os
import sys
import tempfile
import shutil
import re
from pathlib import Path


def robust_rmtree(path: Path, retries: int = 5, delay: int = 1):
    """
    一个更健壮的删除函数，可以处理文件和目录，并在遇到权限错误时重试。
    """
    import time
    for i in range(retries):
        if not path.exists():
            return

        try:
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()

            if not path.exists():
                return

        except (PermissionError, OSError) as e:
            print(f"  [robust_rmtree] Warn: Attempt {i + 1}/{retries} to delete '{path}' failed: {e}. Retrying in {delay}s...")
            time.sleep(delay)

    if path.exists():
        raise OSError(f"Failed to delete path '{path}' after {retries} retries. It might be locked by another process.")


def prepare_workspace_node(state: dict) -> dict:
    """
    为当前设备创建一个隔离的子目录，并将所有项目文件写入其中。
    """
    device_task = state['current_device_task']
    device_id = device_task['internal_device_id']
    device_role = device_task.get('device_role', device_id)
    safe_device_role = re.sub(r'[^a-zA-Z0-9_-]', '', device_role.lower().replace(' ', '_'))

    print(f"\n--- PREPARING WORKSPACE: Creating isolated directory for device '{device_role}' ---")

    project_files = state['project_files'][device_id]
    base_workspace_path = Path(state['workspace_path'])
    device_project_path = base_workspace_path / safe_device_role

    # 清理旧目录（如果存在），确保每次都是全新的开始
    if device_project_path.exists():
        robust_rmtree(device_project_path)
    device_project_path.mkdir(parents=True, exist_ok=True)

    print(f"  -> Writing project files to: '{device_project_path}'")

    for filename, content in project_files.items():
        dest_path = device_project_path / filename
        if filename.endswith('/'):
            dest_path.mkdir(parents=True, exist_ok=True)
            continue
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        dest_path.write_text(content, encoding="utf-8")

    # 将准备好的、设备专用的路径作为 build_dir 返回，供后续节点使用
    return {"build_dir": str(device_project_path)}


def test_workspace_isolation():
    """测试工作区隔离功能"""
    print("=== 测试设备工作区隔离功能 ===")
    
    # 创建临时工作目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_workspace = Path(temp_dir)
        print(f"使用临时工作目录: {temp_workspace}")
        
        # 模拟两个设备的状态
        device1_state = {
            'current_device_task': {
                'internal_device_id': 'device_001',
                'device_role': 'Light Sensor Node'
            },
            'project_files': {
                'device_001': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/sensor.h': '#ifndef SENSOR_H\n#define SENSOR_H\n#endif'
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        device2_state = {
            'current_device_task': {
                'internal_device_id': 'device_002', 
                'device_role': 'Display Controller'
            },
            'project_files': {
                'device_002': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/display.h': '#ifndef DISPLAY_H\n#define DISPLAY_H\n#endif'
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        # 测试第一个设备
        print("\n--- 测试设备1: Light Sensor Node ---")
        result1 = prepare_workspace_node(device1_state)
        device1_dir = Path(result1['build_dir'])
        
        print(f"设备1工作目录: {device1_dir}")
        assert device1_dir.exists(), "设备1工作目录应该存在"
        assert device1_dir.name == 'light_sensor_node', f"设备1目录名应为 'light_sensor_node'，实际为 '{device1_dir.name}'"
        
        # 验证设备1的文件
        assert (device1_dir / 'src/main.cpp').exists(), "设备1的main.cpp应该存在"
        assert (device1_dir / 'lib/sensor.h').exists(), "设备1的sensor.h应该存在"
        
        # 测试第二个设备
        print("\n--- 测试设备2: Display Controller ---")
        result2 = prepare_workspace_node(device2_state)
        device2_dir = Path(result2['build_dir'])
        
        print(f"设备2工作目录: {device2_dir}")
        assert device2_dir.exists(), "设备2工作目录应该存在"
        assert device2_dir.name == 'display_controller', f"设备2目录名应为 'display_controller'，实际为 '{device2_dir.name}'"
        
        # 验证设备2的文件
        assert (device2_dir / 'src/main.cpp').exists(), "设备2的main.cpp应该存在"
        assert (device2_dir / 'lib/display.h').exists(), "设备2的display.h应该存在"
        
        # 验证隔离性：设备1不应该有设备2的文件，反之亦然
        assert not (device1_dir / 'lib/display.h').exists(), "设备1目录不应该包含设备2的文件"
        assert not (device2_dir / 'lib/sensor.h').exists(), "设备2目录不应该包含设备1的文件"
        
        print("\n✅ 所有测试通过！设备工作区隔离功能正常工作。")
        print(f"设备1目录: {device1_dir}")
        print(f"设备2目录: {device2_dir}")
        
        # 列出创建的目录结构
        print("\n--- 创建的目录结构 ---")
        for device_dir in [device1_dir, device2_dir]:
            print(f"\n{device_dir.name}/")
            for root, dirs, files in os.walk(device_dir):
                level = root.replace(str(device_dir), '').count(os.sep)
                indent = ' ' * 2 * (level + 1)
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 2)
                for file in files:
                    print(f"{subindent}{file}")


if __name__ == "__main__":
    try:
        test_workspace_isolation()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
