#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证设备工作区隔离功能是否正常工作
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.langgraph_def.graph_builder import prepare_workspace_node, robust_rmtree
from app.langgraph_def.agent_state import AgentState


def test_workspace_isolation():
    """测试工作区隔离功能"""
    print("=== 测试设备工作区隔离功能 ===")
    
    # 创建临时工作目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_workspace = Path(temp_dir)
        print(f"使用临时工作目录: {temp_workspace}")
        
        # 模拟两个设备的状态
        device1_state = {
            'current_device_task': {
                'internal_device_id': 'device_001',
                'device_role': 'Light Sensor Node'
            },
            'project_files': {
                'device_001': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/sensor.h': '#ifndef SENSOR_H\n#define SENSOR_H\n#endif'
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        device2_state = {
            'current_device_task': {
                'internal_device_id': 'device_002', 
                'device_role': 'Display Controller'
            },
            'project_files': {
                'device_002': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                    'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    'lib/display.h': '#ifndef DISPLAY_H\n#define DISPLAY_H\n#endif'
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        # 测试第一个设备
        print("\n--- 测试设备1: Light Sensor Node ---")
        result1 = prepare_workspace_node(device1_state)
        device1_dir = Path(result1['build_dir'])
        
        print(f"设备1工作目录: {device1_dir}")
        assert device1_dir.exists(), "设备1工作目录应该存在"
        assert device1_dir.name == 'light_sensor_node', f"设备1目录名应为 'light_sensor_node'，实际为 '{device1_dir.name}'"
        
        # 验证设备1的文件
        assert (device1_dir / 'src/main.cpp').exists(), "设备1的main.cpp应该存在"
        assert (device1_dir / 'lib/sensor.h').exists(), "设备1的sensor.h应该存在"
        
        # 测试第二个设备
        print("\n--- 测试设备2: Display Controller ---")
        result2 = prepare_workspace_node(device2_state)
        device2_dir = Path(result2['build_dir'])
        
        print(f"设备2工作目录: {device2_dir}")
        assert device2_dir.exists(), "设备2工作目录应该存在"
        assert device2_dir.name == 'display_controller', f"设备2目录名应为 'display_controller'，实际为 '{device2_dir.name}'"
        
        # 验证设备2的文件
        assert (device2_dir / 'src/main.cpp').exists(), "设备2的main.cpp应该存在"
        assert (device2_dir / 'lib/display.h').exists(), "设备2的display.h应该存在"
        
        # 验证隔离性：设备1不应该有设备2的文件，反之亦然
        assert not (device1_dir / 'lib/display.h').exists(), "设备1目录不应该包含设备2的文件"
        assert not (device2_dir / 'lib/sensor.h').exists(), "设备2目录不应该包含设备1的文件"
        
        print("\n✅ 所有测试通过！设备工作区隔离功能正常工作。")
        print(f"设备1目录: {device1_dir}")
        print(f"设备2目录: {device2_dir}")
        
        # 列出创建的目录结构
        print("\n--- 创建的目录结构 ---")
        for device_dir in [device1_dir, device2_dir]:
            print(f"\n{device_dir.name}/")
            for root, dirs, files in os.walk(device_dir):
                level = root.replace(str(device_dir), '').count(os.sep)
                indent = ' ' * 2 * (level + 1)
                print(f"{indent}{os.path.basename(root)}/")
                subindent = ' ' * 2 * (level + 2)
                for file in files:
                    print(f"{subindent}{file}")


if __name__ == "__main__":
    try:
        test_workspace_isolation()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
