#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证中文设备名称的处理和子文件夹创建
"""

import os
import sys
import tempfile
import re
import shutil
from pathlib import Path


def robust_rmtree(path: Path, retries: int = 5, delay: int = 1):
    """
    一个更健壮的删除函数，可以处理文件和目录，并在遇到权限错误时重试。
    """
    import time
    for i in range(retries):
        if not path.exists():
            return

        try:
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()

            if not path.exists():
                return

        except (PermissionError, OSError) as e:
            print(f"  [robust_rmtree] Warn: Attempt {i + 1}/{retries} to delete '{path}' failed: {e}. Retrying in {delay}s...")
            time.sleep(delay)

    if path.exists():
        raise OSError(f"Failed to delete path '{path}' after {retries} retries. It might be locked by another process.")


def prepare_workspace_node(state: dict) -> dict:
    """
    为当前设备创建一个隔离的子目录，并将所有项目文件写入其中。
    """
    device_task = state['current_device_task']
    device_id = device_task['internal_device_id']
    device_role = device_task.get('device_role', device_id)
    safe_device_role = re.sub(r'[^a-zA-Z0-9_-]', '', device_role.lower().replace(' ', '_'))

    # 🔧 修复：当设备名称转换后为空时，使用 device_id 作为回退
    if not safe_device_role or safe_device_role.strip() == '':
        safe_device_role = device_id
        print(f"\n--- PREPARING WORKSPACE: Device role '{device_role}' contains non-ASCII characters, using device_id '{device_id}' as folder name ---")
    else:
        print(f"\n--- PREPARING WORKSPACE: Creating isolated directory for device '{device_role}' -> '{safe_device_role}' ---")

    print(f"  -> Original device_role: '{device_role}'")
    print(f"  -> Safe device_role: '{safe_device_role}'")

    project_files = state['project_files'][device_id]
    base_workspace_path = Path(state['workspace_path'])
    device_project_path = base_workspace_path / safe_device_role
    
    print(f"  -> Base workspace path: '{base_workspace_path}'")
    print(f"  -> Device project path: '{device_project_path}'")
    
    # 清理旧目录（如果存在），确保每次都是全新的开始
    if device_project_path.exists():
        print(f"  -> Removing existing directory: '{device_project_path}'")
        robust_rmtree(device_project_path)
    
    print(f"  -> Creating directory: '{device_project_path}'")
    device_project_path.mkdir(parents=True, exist_ok=True)
    
    print(f"  -> Writing project files to: '{device_project_path}'")

    for filename, content in project_files.items():
        dest_path = device_project_path / filename
        print(f"    -> Writing file: '{dest_path}'")
        if filename.endswith('/'):
            dest_path.mkdir(parents=True, exist_ok=True)
            continue
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        dest_path.write_text(content, encoding="utf-8")
    
    # 将准备好的、设备专用的路径作为 build_dir 返回，供后续节点使用
    print(f"  -> Returning build_dir: '{device_project_path}'")
    return {"build_dir": str(device_project_path)}


def test_chinese_device_names():
    """测试中文设备名称的处理"""
    print("=== 测试中文设备名称的处理 ===")
    
    test_cases = [
        {
            'device_role': '光照采集端',
            'expected_safe_name': ''  # 中文字符会被完全过滤掉
        },
        {
            'device_role': 'Light Sensor 光照传感器',
            'expected_safe_name': 'light_sensor_'  # 中文部分被过滤，英文部分保留
        },
        {
            'device_role': '显示控制器 Display Controller',
            'expected_safe_name': '_display_controller'  # 中文部分被过滤
        },
        {
            'device_role': 'Light Sensor Node',
            'expected_safe_name': 'light_sensor_node'  # 纯英文，正常处理
        }
    ]
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_workspace = Path(temp_dir)
        print(f"使用临时工作目录: {temp_workspace}")
        
        for i, test_case in enumerate(test_cases):
            print(f"\n--- 测试用例 {i+1}: '{test_case['device_role']}' ---")
            
            # 测试设备名称转换
            device_role = test_case['device_role']
            safe_device_role = re.sub(r'[^a-zA-Z0-9_-]', '', device_role.lower().replace(' ', '_'))
            
            print(f"原始名称: '{device_role}'")
            print(f"安全名称: '{safe_device_role}'")
            print(f"预期名称: '{test_case['expected_safe_name']}'")
            
            if safe_device_role == test_case['expected_safe_name']:
                print("✅ 名称转换正确")
            else:
                print(f"❌ 名称转换错误，预期 '{test_case['expected_safe_name']}'，实际 '{safe_device_role}'")
            
            # 如果转换后的名称为空，跳过文件夹创建测试
            if not safe_device_role:
                print("⚠️  转换后名称为空，将跳过文件夹创建测试")
                continue
            
            # 创建测试状态
            test_state = {
                'current_device_task': {
                    'internal_device_id': f'device_{i+1:03d}',
                    'device_role': device_role
                },
                'project_files': {
                    f'device_{i+1:03d}': {
                        'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                        'platformio.ini': '[env:esp32dev]\nplatform = espressif32\nboard = esp32dev',
                    }
                },
                'workspace_path': str(temp_workspace)
            }
            
            try:
                # 调用函数
                result = prepare_workspace_node(test_state)
                
                # 验证结果
                if 'build_dir' in result:
                    build_dir = Path(result['build_dir'])
                    if build_dir.exists():
                        print(f"✅ 成功创建目录: {build_dir}")
                        
                        # 验证文件是否存在
                        if (build_dir / 'src/main.cpp').exists():
                            print("✅ 文件创建成功")
                        else:
                            print("❌ 文件创建失败")
                    else:
                        print(f"❌ 目录创建失败: {build_dir}")
                else:
                    print("❌ 未返回 build_dir")
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 显示最终的目录结构
        print(f"\n--- 最终目录结构 ---")
        for root, dirs, files in os.walk(temp_workspace):
            level = root.replace(str(temp_workspace), '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")


def test_empty_device_name_fallback():
    """测试当设备名称转换为空时的回退机制"""
    print("\n=== 测试空设备名称的回退机制 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_workspace = Path(temp_dir)
        
        test_state = {
            'current_device_task': {
                'internal_device_id': 'device_001',
                'device_role': '光照采集端'  # 纯中文，会被完全过滤
            },
            'project_files': {
                'device_001': {
                    'src/main.cpp': '#include <Arduino.h>\nvoid setup() {}\nvoid loop() {}',
                }
            },
            'workspace_path': str(temp_workspace)
        }
        
        try:
            result = prepare_workspace_node(test_state)
            build_dir = Path(result['build_dir'])
            
            if build_dir.exists():
                print(f"✅ 即使设备名称为空，也成功创建了目录: {build_dir}")
                print(f"目录名称: '{build_dir.name}'")
            else:
                print(f"❌ 目录创建失败: {build_dir}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主测试函数"""
    print("🔍 测试中文设备名称处理和子文件夹创建...")
    
    try:
        test_chinese_device_names()
        test_empty_device_name_fallback()
        
        print("\n💡 建议：")
        print("1. 如果设备名称包含中文，建议在系统设计时使用英文名称")
        print("2. 或者修改 safe_device_role 的生成逻辑，添加中文到拼音的转换")
        print("3. 当转换后名称为空时，可以使用 device_id 作为回退")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
